from tkinter import *
class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.creatmenu()
        self.createWidgets()
    def creatmenu(self):
        self.menubar = <PERSON>u(self.master)

        self.file_menu = <PERSON>u(self.menubar, tearoff=0)
        self.edit_menu = <PERSON>u(self.menubar, tearoff=0)
        self.format_menu = <PERSON>u(self.menubar, tearoff=0)
        self.view_menu = <PERSON>u(self.menubar, tearoff=0)

        self.menubar.add_cascade(label="文件", )
        self.menubar.add_cascade(label="编辑", )
        self.menubar.add_cascade(label="格式", )
        self.menubar.add_cascade(label="查看", )

        self.file_menu.add_command(label="新建", )



        self.master.config(menu=self.menubar)


    def createWidgets(self):
        pass
if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()