from tkinter import *
class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.cratemenu()
        self.createWidget()

    def cratemenu(self):
        # 创建菜单栏

        self.menubar = <PERSON>u(self.master)

        file_menu = <PERSON>u(self.menubar, tearoff=0)
        file_menu = Menu(self.menubar, tearoff=0)
        file_menu = Menu(self.menubar, tearoff=0)
        file_menu = Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="文件", menu=file_menu)
        self.menubar.add_cascade(label="编辑", menu=file_menu)
        self.menubar.add_cascade(label="格式", menu=file_menu)
        self.menubar.add_cascade(label="查看", menu=file_menu)

        file_menu.add_command(label="新建", )

        self.master.config(menu=self.menubar)  



    def createWidget(self):
        pass

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()
