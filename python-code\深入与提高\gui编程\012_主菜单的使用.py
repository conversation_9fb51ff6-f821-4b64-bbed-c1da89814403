from tkinter import *
class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.cratemenu()
        self.createWidget()

    def cratemenu(self):
        # 1. 创建主菜单栏
        self.menubar = <PERSON>u(self.master)

        # 2. 创建子菜单
        file_menu = Menu(self.menubar, tearoff=0)    # "文件"子菜单
        edit_menu = Menu(self.menubar, tearoff=0)    # "编辑"子菜单
        help_menu = Menu(self.menubar, tearoff=0)    # "帮助"子菜单
        view_menu = Menu(self.menubar, tearoff=0)    # "查看"子菜单

        # 3. 将子菜单添加到主菜单栏
        self.menubar.add_cascade(label="文件", menu=file_menu)
        self.menubar.add_cascade(label="编辑", menu=edit_menu)
        self.menubar.add_cascade(label="帮助", menu=help_menu)
        self.menubar.add_cascade(label="查看", menu=view_menu)
        # 4. 向子菜单添加具体的功能命令
        file_menu.add_command(label="新建", )
        file_menu.add_command(label="打开", )
        
        file_menu.add_separator()  # 添加分割线
        file_menu.add_command(label="保存",)
        file_menu.add_command(label="另存为",)
        file_menu.add_separator()  # 添加分割线
        file_menu.add_command(label="退出",)

        # 5. 将菜单栏显示到窗口上
        self.master.config(menu=self.menubar)



    def createWidget(self):
        pass

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()
