from tkinter import *

class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()
    def createWidget(self):
        self.v1 = StringVar()
       

        OptionMenu(self, text="选项菜单", value="选项1", values=("选项1", "选项2", "选项3"), variable=self.v1).pack()

        self.btn01 = Button(self, text="确定", command=self.confirm)
        self.btn01.pack()
    def confirm(self):
        print(self.v1.get())

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()