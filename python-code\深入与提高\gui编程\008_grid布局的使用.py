from tkinter import *

class Application(Frame):
    def __init__(self,master=None):
        super().__init__(master)
        self.master = master
        self.pack()
        self.createWidget()

    def createWidget(self):
        self.label01 = Label(self, text="用户名")
        self.label01.grid(row=0, column=1)
        self.entry01 = Entry(self)
        self.entry01.grid(row=0, column=2)
        self.label02 = Label(self,text='用户名为手机号')
        self.label02.grid(row=0,column=3,sticky='nsew')

        self.label03 = Label(self, text="密码")
        self.label03.grid(row=1, column=1)
        self.entry02 = Entry(self, show="*")
        self.entry02.grid(row=1, column=2)

        self.btn01 = Button(self, text="登录")
        self.btn01.grid(row=2, column=2)

        self.lable04 = Lable(self,text="取消")
        self.lable04.grid(row=, column=3)

if __name__ == "__main__":
    root = Tk()
    root.title("第一个GUI程序")
    root.geometry("500x500+500+300")
    app = Application(root)
    app.mainloop()